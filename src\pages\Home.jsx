import './Home.css'

const Home = () => {
  return (
    <div className="home">
      {/* Hero Section */}
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <h1 className="hero-title">
              Innovative Software Solutions for Your Business
            </h1>
            <p className="hero-subtitle">
              We create cutting-edge software applications that drive growth, 
              efficiency, and success for businesses of all sizes.
            </p>
            <div className="hero-buttons">
              <a href="/services" className="btn btn-primary">Our Services</a>
              <a href="/contact" className="btn btn-secondary">Get Started</a>
            </div>
          </div>
          <div className="hero-image">
            <div className="hero-placeholder">
              <span>🚀</span>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="services-overview">
        <div className="container">
          <h2 className="section-title">What We Do</h2>
          <div className="services-grid">
            <div className="service-card">
              <div className="service-icon">💻</div>
              <h3>Web Development</h3>
              <p>Custom web applications built with modern technologies and best practices.</p>
            </div>
            <div className="service-card">
              <div className="service-icon">📱</div>
              <h3>Mobile Apps</h3>
              <p>Native and cross-platform mobile applications for iOS and Android.</p>
            </div>
            <div className="service-card">
              <div className="service-icon">☁️</div>
              <h3>Cloud Solutions</h3>
              <p>Scalable cloud infrastructure and deployment solutions.</p>
            </div>
            <div className="service-card">
              <div className="service-icon">🔧</div>
              <h3>Consulting</h3>
              <p>Expert technical consulting and software architecture guidance.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="why-choose-us">
        <div className="container">
          <h2 className="section-title">Why Choose Eria Software?</h2>
          <div className="features-grid">
            <div className="feature">
              <h3>Expert Team</h3>
              <p>Our experienced developers use the latest technologies and best practices.</p>
            </div>
            <div className="feature">
              <h3>Quality Assurance</h3>
              <p>Rigorous testing ensures reliable, bug-free software solutions.</p>
            </div>
            <div className="feature">
              <h3>On-Time Delivery</h3>
              <p>We meet deadlines and deliver projects on schedule, every time.</p>
            </div>
            <div className="feature">
              <h3>24/7 Support</h3>
              <p>Continuous support and maintenance for all our software solutions.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <h2>Ready to Transform Your Business?</h2>
          <p>Let's discuss how we can help you achieve your goals with custom software solutions.</p>
          <a href="/contact" className="btn btn-primary">Contact Us Today</a>
        </div>
      </section>
    </div>
  )
}

export default Home
