.header {
  background: var(--white);
  box-shadow: 0 2px 10px rgba(30, 64, 175, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand .logo {
  text-decoration: none;
  color: var(--text-primary);
}

.logo-text {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--primary-blue);
}

.nav-menu {
  display: flex;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav-item {
  margin: 0;
}

.nav-link {
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: var(--primary-blue);
  background-color: var(--extra-light-blue);
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .nav-list {
    gap: 1rem;
  }
  
  .nav-link {
    padding: 0.5rem;
    font-size: 0.9rem;
  }
}
