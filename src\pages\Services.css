.services {
  padding-top: 80px;
}

/* Services Hero */
.services-hero {
  background: linear-gradient(135deg, var(--primary-blue-light) 0%, var(--primary-blue) 100%);
  color: var(--white);
  padding: 4rem 0;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Main Services */
.main-services {
  padding: 4rem 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 3rem;
}

.service-detail {
  background: var(--white);
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(30, 64, 175, 0.1);
  border-left: 4px solid var(--primary-blue);
}

.service-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.service-icon {
  font-size: 2.5rem;
  color: var(--primary-blue);
}

.service-detail h2 {
  font-size: 1.8rem;
  color: var(--text-primary);
  margin: 0;
}

.service-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.service-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.service-features li {
  padding: 0.5rem 0;
  color: var(--text-primary);
  position: relative;
  padding-left: 1.5rem;
}

.service-features li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--primary-blue);
  font-weight: bold;
}

/* Process Section */
.process {
  background: var(--extra-light-blue);
  padding: 4rem 0;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: var(--text-primary);
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.process-step {
  text-align: center;
  padding: 2rem 1rem;
}

.step-number {
  width: 60px;
  height: 60px;
  background: var(--primary-blue);
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 1rem;
}

.process-step h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.process-step p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Technologies */
.technologies {
  padding: 4rem 0;
}

.tech-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.tech-category {
  background: var(--white);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(30, 64, 175, 0.1);
}

.tech-category h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--primary-blue);
  text-align: center;
}

.tech-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-item {
  background: var(--light-blue);
  color: var(--primary-blue-dark);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Services CTA */
.services-cta {
  background: var(--primary-blue-dark);
  color: var(--white);
  padding: 4rem 0;
  text-align: center;
}

.services-cta h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.services-cta p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.btn {
  padding: 0.75rem 2rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-block;
}

.btn-primary {
  background: var(--primary-blue-light);
  color: var(--white);
  border: 2px solid var(--primary-blue-light);
}

.btn-primary:hover {
  background: var(--primary-blue);
  border-color: var(--primary-blue);
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .service-detail {
    padding: 2rem;
  }
  
  .service-header {
    flex-direction: column;
    text-align: center;
  }
  
  .process-steps {
    grid-template-columns: 1fr;
  }
  
  .tech-categories {
    grid-template-columns: 1fr;
  }
  
  .section-title {
    font-size: 2rem;
  }
}
