import './Services.css'

const Services = () => {
  return (
    <div className="services">
      {/* Hero Section */}
      <section className="services-hero">
        <div className="container">
          <h1 className="page-title">Our Services</h1>
          <p className="page-subtitle">
            Comprehensive software development solutions tailored to your business needs
          </p>
        </div>
      </section>

      {/* Main Services */}
      <section className="main-services">
        <div className="container">
          <div className="services-grid">
            <div className="service-detail">
              <div className="service-header">
                <div className="service-icon">💻</div>
                <h2>Web Development</h2>
              </div>
              <p className="service-description">
                Custom web applications built with modern frameworks and technologies. 
                We create responsive, scalable, and secure web solutions.
              </p>
              <ul className="service-features">
                <li>React, Vue.js, Angular applications</li>
                <li>Node.js, Python, PHP backends</li>
                <li>Responsive design & mobile optimization</li>
                <li>E-commerce platforms</li>
                <li>Content Management Systems</li>
              </ul>
            </div>

            <div className="service-detail">
              <div className="service-header">
                <div className="service-icon">📱</div>
                <h2>Mobile App Development</h2>
              </div>
              <p className="service-description">
                Native and cross-platform mobile applications for iOS and Android 
                that deliver exceptional user experiences.
              </p>
              <ul className="service-features">
                <li>iOS (Swift) & Android (Kotlin) native apps</li>
                <li>React Native & Flutter cross-platform</li>
                <li>App Store optimization</li>
                <li>Push notifications & analytics</li>
                <li>Offline functionality</li>
              </ul>
            </div>

            <div className="service-detail">
              <div className="service-header">
                <div className="service-icon">☁️</div>
                <h2>Cloud Solutions</h2>
              </div>
              <p className="service-description">
                Scalable cloud infrastructure and deployment solutions using 
                leading cloud platforms for optimal performance.
              </p>
              <ul className="service-features">
                <li>AWS, Azure, Google Cloud deployment</li>
                <li>Microservices architecture</li>
                <li>DevOps & CI/CD pipelines</li>
                <li>Database management & optimization</li>
                <li>Security & compliance</li>
              </ul>
            </div>

            <div className="service-detail">
              <div className="service-header">
                <div className="service-icon">🔧</div>
                <h2>Technical Consulting</h2>
              </div>
              <p className="service-description">
                Expert guidance on technology strategy, architecture decisions, 
                and digital transformation initiatives.
              </p>
              <ul className="service-features">
                <li>Technology stack recommendations</li>
                <li>Code reviews & audits</li>
                <li>Performance optimization</li>
                <li>Team training & mentoring</li>
                <li>Project planning & estimation</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="process">
        <div className="container">
          <h2 className="section-title">Our Development Process</h2>
          <div className="process-steps">
            <div className="process-step">
              <div className="step-number">1</div>
              <h3>Discovery</h3>
              <p>We analyze your requirements and define project scope and objectives.</p>
            </div>
            <div className="process-step">
              <div className="step-number">2</div>
              <h3>Planning</h3>
              <p>Detailed project planning, architecture design, and timeline creation.</p>
            </div>
            <div className="process-step">
              <div className="step-number">3</div>
              <h3>Development</h3>
              <p>Agile development with regular updates and milestone deliveries.</p>
            </div>
            <div className="process-step">
              <div className="step-number">4</div>
              <h3>Testing</h3>
              <p>Comprehensive testing to ensure quality and performance standards.</p>
            </div>
            <div className="process-step">
              <div className="step-number">5</div>
              <h3>Deployment</h3>
              <p>Smooth deployment and launch with ongoing support and maintenance.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Technologies */}
      <section className="technologies">
        <div className="container">
          <h2 className="section-title">Technologies We Use</h2>
          <div className="tech-categories">
            <div className="tech-category">
              <h3>Frontend</h3>
              <div className="tech-list">
                <span className="tech-item">React</span>
                <span className="tech-item">Vue.js</span>
                <span className="tech-item">Angular</span>
                <span className="tech-item">TypeScript</span>
                <span className="tech-item">Next.js</span>
              </div>
            </div>
            <div className="tech-category">
              <h3>Backend</h3>
              <div className="tech-list">
                <span className="tech-item">Node.js</span>
                <span className="tech-item">Python</span>
                <span className="tech-item">Java</span>
                <span className="tech-item">PHP</span>
                <span className="tech-item">.NET</span>
              </div>
            </div>
            <div className="tech-category">
              <h3>Mobile</h3>
              <div className="tech-list">
                <span className="tech-item">React Native</span>
                <span className="tech-item">Flutter</span>
                <span className="tech-item">Swift</span>
                <span className="tech-item">Kotlin</span>
                <span className="tech-item">Xamarin</span>
              </div>
            </div>
            <div className="tech-category">
              <h3>Cloud & DevOps</h3>
              <div className="tech-list">
                <span className="tech-item">AWS</span>
                <span className="tech-item">Azure</span>
                <span className="tech-item">Docker</span>
                <span className="tech-item">Kubernetes</span>
                <span className="tech-item">Jenkins</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="services-cta">
        <div className="container">
          <h2>Ready to Start Your Project?</h2>
          <p>Let's discuss your requirements and create a solution that fits your needs.</p>
          <a href="/contact" className="btn btn-primary">Get a Quote</a>
        </div>
      </section>
    </div>
  )
}

export default Services
