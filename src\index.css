* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Blue and White Theme Color Palette */
  --primary-blue: #1e40af;
  --primary-blue-dark: #1e3a8a;
  --primary-blue-light: #3b82f6;
  --secondary-blue: #60a5fa;
  --accent-blue: #93c5fd;
  --light-blue: #dbeafe;
  --extra-light-blue: #eff6ff;

  --white: #ffffff;
  --off-white: #f8fafc;
  --light-gray: #f1f5f9;
  --medium-gray: #64748b;
  --dark-gray: #334155;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-muted: #64748b;

  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: var(--text-primary);
  background-color: var(--white);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: var(--white);
}

#root {
  min-height: 100vh;
}

a {
  color: inherit;
  text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
}

p {
  line-height: 1.6;
}

ul, ol {
  list-style: none;
}

button {
  font-family: inherit;
  cursor: pointer;
}

img {
  max-width: 100%;
  height: auto;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 2rem; }
