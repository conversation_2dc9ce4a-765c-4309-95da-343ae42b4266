.footer {
  background: var(--primary-blue-dark);
  color: var(--white);
  padding: 3rem 0 1rem;
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3.footer-title {
  color: var(--accent-blue);
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.footer-section h4.footer-subtitle {
  color: var(--light-blue);
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.footer-description {
  color: var(--secondary-blue);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: var(--secondary-blue);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--accent-blue);
}

.contact-info p {
  color: var(--secondary-blue);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-bottom {
  border-top: 1px solid var(--primary-blue);
  padding-top: 1rem;
  text-align: center;
}

.footer-bottom p {
  color: var(--secondary-blue);
  margin: 0;
}

@media (max-width: 768px) {
  .footer {
    padding: 2rem 0 1rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
