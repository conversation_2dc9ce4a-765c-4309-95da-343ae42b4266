.about {
  padding-top: 80px;
}

/* About Hero */
.about-hero {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Company Story */
.company-story {
  padding: 4rem 0;
}

.story-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.story-text h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.story-text p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #6b7280;
  margin-bottom: 1.5rem;
}

.story-image {
  display: flex;
  justify-content: center;
}

.image-placeholder {
  width: 300px;
  height: 300px;
  background: #f3f4f6;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
}

/* Mission & Vision */
.mission-vision {
  background: #f8fafc;
  padding: 4rem 0;
}

.mv-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.mv-card {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.mv-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.mv-card h3 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: #2563eb;
}

.mv-card p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #6b7280;
}

/* Team Section */
.team {
  padding: 4rem 0;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.team-member {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
}

.member-photo {
  width: 100px;
  height: 100px;
  background: #e5e7eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1rem;
}

.team-member h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.member-role {
  color: #2563eb;
  font-weight: 600;
  margin-bottom: 1rem;
}

.member-bio {
  color: #6b7280;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* Values */
.values {
  background: #f8fafc;
  padding: 4rem 0;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.value-item {
  text-align: center;
  padding: 2rem;
}

.value-item h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #2563eb;
}

.value-item p {
  color: #6b7280;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }
  
  .story-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .story-text h2 {
    font-size: 2rem;
  }
  
  .image-placeholder {
    width: 200px;
    height: 200px;
    font-size: 3rem;
  }
  
  .mv-grid {
    grid-template-columns: 1fr;
  }
  
  .team-grid,
  .values-grid {
    grid-template-columns: 1fr;
  }
  
  .section-title {
    font-size: 2rem;
  }
}
