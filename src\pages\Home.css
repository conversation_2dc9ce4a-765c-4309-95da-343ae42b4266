.home {
  padding-top: 80px; /* Account for fixed header */
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
  color: var(--white);
  padding: 4rem 0;
  min-height: 70vh;
  display: flex;
  align-items: center;
}

.hero .container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 2rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-block;
}

.btn-primary {
  background: var(--primary-blue-light);
  color: var(--white);
  border: 2px solid var(--primary-blue-light);
}

.btn-primary:hover {
  background: var(--primary-blue);
  border-color: var(--primary-blue);
}

.btn-secondary {
  background: transparent;
  color: var(--white);
  border: 2px solid var(--white);
}

.btn-secondary:hover {
  background: var(--white);
  color: var(--primary-blue);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-placeholder {
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
}

/* Services Overview */
.services-overview {
  padding: 4rem 0;
  background: var(--extra-light-blue);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: var(--text-primary);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.service-card {
  background: var(--white);
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(30, 64, 175, 0.1);
  transition: transform 0.3s ease;
  border-top: 3px solid var(--primary-blue-light);
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(30, 64, 175, 0.15);
}

.service-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--primary-blue);
}

.service-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.service-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Why Choose Us */
.why-choose-us {
  padding: 4rem 0;
  background: var(--white);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature {
  text-align: center;
  padding: 1rem;
}

.feature h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--primary-blue);
}

.feature p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* CTA Section */
.cta {
  background: var(--primary-blue-dark);
  color: var(--white);
  padding: 4rem 0;
  text-align: center;
}

.cta h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.cta p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero .container {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-buttons {
    justify-content: center;
  }
  
  .hero-placeholder {
    width: 200px;
    height: 200px;
    font-size: 3rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .services-grid,
  .features-grid {
    grid-template-columns: 1fr;
  }
}
